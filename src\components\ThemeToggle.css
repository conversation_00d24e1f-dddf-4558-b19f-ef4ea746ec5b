/* Theme toggle styles */
.switch {
  font-size: 17px;
  position: relative;
  display: inline-block;
  width: 3.5em;
  height: 2em;
  transform-style: preserve-3d;
  perspective: 500px;
}

.switch::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
  border-radius: 50px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fdfefedc;
  transition: .4s;
  border-radius: 30px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 1.4em;
  width: 1.4em;
  left: 0.3em;
  bottom: 0.35em;
  transition: .4s;
  border-radius: 50%;
  box-shadow: rgba(0, 0, 0, 0.17) 0px -10px 10px 0px inset;
  background-color: #ff99fd;
  background-image: radial-gradient(at 81% 39%, hsla(327,79%,79%,1) 0px, transparent 50%),
  radial-gradient(at 11% 72%, hsla(264,64%,79%,1) 0px, transparent 50%),
  radial-gradient(at 23% 20%, hsla(75,98%,71%,1) 0px, transparent 50%);
}

.input__check:checked + .slider {
  background-color: #17202A;
}

.input__check:checked + .slider:before {
  transform: translateX(1.5em);
}

.theme-toggle {
  background: linear-gradient(145deg, #e6e6e6, #ffffff);
  border: none;
  outline: none;
  backdrop-filter: blur(8px);
}

.dark .theme-toggle {
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
}

.theme-toggle:hover {
  background: linear-gradient(145deg, #f0f0f0, #e0e0e0);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.05),
    0 4px 8px rgba(0, 0, 0, 0.05),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.4);
}

.dark .theme-toggle:hover {
  background: linear-gradient(145deg, #323232, #222222);
  box-shadow: 
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.2),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.1);
}

.theme-toggle svg {
  color: #666;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.dark .theme-toggle svg {
  color: #999;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}

.theme-toggle:hover svg {
  color: #333;
  filter: 
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1))
    drop-shadow(0 0 4px rgba(255, 255, 255, 0.4));
}

.dark .theme-toggle:hover svg {
  color: #fff;
  filter: 
    drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2))
    drop-shadow(0 0 4px rgba(255, 255, 255, 0.2));
} 