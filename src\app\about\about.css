/* Ensure scrolling works properly */
html, body {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

body {
  min-height: 100vh;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.05)
  );
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.1)
  );
}

/* Fade-in animation for sections */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

/* Subtle floating animation */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

/* Text reveal animation */
@keyframes textReveal {
  from {
    clip-path: inset(0 100% 0 0);
  }
  to {
    clip-path: inset(0 0 0 0);
  }
}

/* Gradient shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.animate-fade-in {
  animation: fadeIn 1s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

/* Metallic effects */
.metallic-text {
  background: linear-gradient(
    90deg, 
    #d0d0d0 0%, 
    #f5f5f5 25%, 
    #d0d0d0 50%, 
    #f5f5f5 75%, 
    #d0d0d0 100%
  );
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: shimmer 8s linear infinite;
}

.metallic-border {
  position: relative;
  overflow: hidden;
}

.metallic-border::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.2) 25%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    rgba(255, 255, 255, 0.05) 100%
  );
  background-size: 200% auto;
  animation: shimmer 10s linear infinite;
}

.hover-lift {
  transition: transform 0.3s cubic-bezier(0.22, 1, 0.36, 1), box-shadow 0.3s cubic-bezier(0.22, 1, 0.36, 1);
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.05), 
              0 1px 3px rgba(255, 255, 255, 0.05);
}

.text-reveal {
  animation: textReveal 1s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
}

.floating {
  animation: float 6s ease-in-out infinite;
}

/* Ensure sections stay visible after animation */
section {
  backface-visibility: hidden;
}

/* Staggered animation for sections */
section:nth-child(1).animate-fade-in { animation-delay: 0.1s; }
section:nth-child(2).animate-fade-in { animation-delay: 0.3s; }
section:nth-child(3).animate-fade-in { animation-delay: 0.5s; }
section:nth-child(4).animate-fade-in { animation-delay: 0.7s; }
section:nth-child(5).animate-fade-in { animation-delay: 0.9s; }
footer.animate-fade-in { animation-delay: 1.1s; }

/* Radial glass effect */
.glass-radial {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
  background: radial-gradient(
    circle at var(--x, 50%) var(--y, 50%),
    rgba(255, 255, 255, 0.03) 0%,
    transparent 60%
  );
} 