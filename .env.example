# EmpathAI Environment Configuration
# Copy this file to .env.local and fill in your API keys

# REQUIRED: Google Gemini API Key for fastest AI responses
# Get your key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: App Configuration
NEXT_PUBLIC_APP_NAME=EmpathAI

# Optional: Clerk Authentication (if using auth features)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# Optional: Supabase (if using database features)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Voice Optimization Settings (optional, defaults to optimal settings)
VOICE_RESPONSE_RATE=1.1
VOICE_RESPONSE_VOLUME=0.9
VOICE_RESPONSE_PITCH=1.0
