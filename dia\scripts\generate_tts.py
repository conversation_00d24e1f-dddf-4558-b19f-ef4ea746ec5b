#!/usr/bin/env python3
"""
NARI Labs Dia TTS Generation Script
Used by the Next.js API to generate high-quality text-to-speech using the Dia model.
"""

import argparse
import sys
import os
from pathlib import Path

# Add the dia module to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from dia.model import Dia


def main():
    parser = argparse.ArgumentParser(description='Generate TTS using NARI Labs Dia model')
    parser.add_argument('--text', required=True, help='Text to convert to speech')
    parser.add_argument('--output', required=True, help='Output audio file path')
    parser.add_argument('--max-tokens', type=int, default=3072, help='Maximum tokens to generate')
    parser.add_argument('--cfg-scale', type=float, default=3.0, help='CFG scale for guidance')
    parser.add_argument('--temperature', type=float, default=1.8, help='Temperature for sampling')
    parser.add_argument('--top-p', type=float, default=0.90, help='Top-p value for nucleus sampling')
    parser.add_argument('--cfg-filter-top-k', type=int, default=45, help='CFG filter top-k value')
    parser.add_argument('--audio-prompt', help='Audio prompt file for voice cloning')
    parser.add_argument('--compute-dtype', default='float16', choices=['float32', 'float16', 'bfloat16'],
                        help='Compute dtype for model inference')
    
    args = parser.parse_args()
    
    try:
        print(f"Initializing NARI Labs Dia model with dtype: {args.compute_dtype}")
        
        # Load the Dia model
        model = Dia.from_pretrained(
            "nari-labs/Dia-1.6B-0626", 
            compute_dtype=args.compute_dtype
        )
        
        print(f"Model loaded successfully. Generating speech for: '{args.text[:50]}...'")
        
        # Format text for Dia model if needed
        formatted_text = format_text_for_dia(args.text)
        
        # Prepare audio prompt if provided
        audio_prompt = None
        if args.audio_prompt and os.path.exists(args.audio_prompt):
            print(f"Loading audio prompt: {args.audio_prompt}")
            audio_prompt = model.load_audio(args.audio_prompt)
        
        # Generate speech
        output_audio = model.generate(
            formatted_text,
            max_tokens=args.max_tokens,
            cfg_scale=args.cfg_scale,
            temperature=args.temperature,
            top_p=args.top_p,
            cfg_filter_top_k=args.cfg_filter_top_k,
            use_torch_compile=False,  # Keep False for stability
            audio_prompt=audio_prompt,
            verbose=True
        )
        
        if output_audio is not None:
            # Ensure output directory exists
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save the generated audio
            model.save_audio(args.output, output_audio)
            print(f"Audio saved successfully to: {args.output}")
            print(f"Audio shape: {output_audio.shape if hasattr(output_audio, 'shape') else 'N/A'}")
        else:
            print("Error: No audio was generated")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error generating TTS: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def format_text_for_dia(text):
    """
    Format text for Dia model by adding speaker tags if not present.
    """
    # Check if text already has speaker tags
    if '[S1]' in text or '[S2]' in text:
        return text
    
    # Add speaker tag for single speaker
    return f"[S1] {text}"


if __name__ == "__main__":
    main()
