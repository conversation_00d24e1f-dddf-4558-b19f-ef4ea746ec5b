import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import { writeFile, unlink, readFile } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      text, 
      max_tokens = 3072, 
      cfg_scale = 3.0, 
      temperature = 1.8, 
      top_p = 0.90, 
      cfg_filter_top_k = 45,
      audio_prompt = null 
    } = body;

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    console.log(`[Dia TTS] Generating speech for: "${text.substring(0, 50)}..."`);

    // Generate unique filename for output
    const outputId = uuidv4();
    const outputPath = join(process.cwd(), 'temp', `dia_output_${outputId}.mp3`);
    
    // Ensure temp directory exists
    const tempDir = join(process.cwd(), 'temp');
    try {
      await writeFile(join(tempDir, '.gitkeep'), '');
    } catch (error) {
      // Directory might already exist, that's fine
    }

    // Create the Python script arguments
    const args = [
      join(process.cwd(), 'dia', 'scripts', 'generate_tts.py'),
      '--text', text,
      '--output', outputPath,
      '--max-tokens', max_tokens.toString(),
      '--cfg-scale', cfg_scale.toString(),
      '--temperature', temperature.toString(),
      '--top-p', top_p.toString(),
      '--cfg-filter-top-k', cfg_filter_top_k.toString()
    ];

    if (audio_prompt) {
      args.push('--audio-prompt', audio_prompt);
    }

    // Run the Python script
    const pythonProcess = spawn('python', args, {
      cwd: join(process.cwd(), 'dia'),
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Wait for the process to complete
    await new Promise((resolve, reject) => {
      pythonProcess.on('close', (code) => {
        if (code === 0) {
          resolve(code);
        } else {
          reject(new Error(`Python process exited with code ${code}. stderr: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(error);
      });
    });

    console.log(`[Dia TTS] Generation completed. Output: ${stdout}`);

    // Read the generated audio file
    const audioBuffer = await readFile(outputPath);
    
    // Clean up the temporary file
    await unlink(outputPath).catch(console.warn);

    // Return the audio file
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.length.toString(),
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('[Dia TTS] Error:', error);
    return NextResponse.json(
      { error: 'Failed to generate speech', details: error.message },
      { status: 500 }
    );
  }
}
