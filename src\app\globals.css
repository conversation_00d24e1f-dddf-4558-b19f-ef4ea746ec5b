@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html, body {
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
  }
  
  body {
    @apply bg-background text-foreground;
    position: fixed;
  }
}

/* Space water drop animation */
.loader {
  width: 20em;
  height: 20em;
  position: relative;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #00dbde, #00a5ff 40%, #0039e6 60%, #001f80);
  box-shadow: 0 0 60px rgba(0, 221, 255, 0.6);
  animation: float 8s ease-in-out infinite, wiggle 6s ease-in-out infinite alternate;
  transition: transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}

/* Scaled loader for chat mode */
.loader.scale-90 {
  transform: scale(0.9);
  box-shadow: 0 0 70px rgba(0, 221, 255, 0.65);
}

/* Clickable water drop hover effects */
.cursor-pointer .loader:hover {
  transform: scale(1.05);
  box-shadow: 0 0 80px rgba(0, 221, 255, 0.8);
}

.cursor-pointer .loader.scale-90:hover {
  transform: scale(0.95);
}

/* Active state for water drop when listening */
.active-visualizer .loader {
  background: radial-gradient(circle at 30% 30%, #ff0055, #ff3377 40%, #aa0066 60%, #550033);
  box-shadow: 0 0 60px rgba(255, 0, 85, 0.6);
  animation: float 4s ease-in-out infinite, wiggle 3s ease-in-out infinite alternate;
}

/* AI Speaking state for water drop - same red as listening */
.ai-speaking .loader {
  background: radial-gradient(circle at 30% 30%, #ff0055, #ff3377 40%, #aa0066 60%, #550033);
  box-shadow: 0 0 60px rgba(255, 0, 85, 0.6);
  animation: float 3s ease-in-out infinite, wiggle 2s ease-in-out infinite alternate;
}

/* Pulsing animation for AI speaking */
.pulsing .loader {
  animation: float 3s ease-in-out infinite, wiggle 2s ease-in-out infinite alternate, pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 60px rgba(255, 0, 85, 0.6);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 80px rgba(255, 0, 85, 0.8);
  }
}

/* Active visualizer hover effect */
.active-visualizer .loader:hover {
  transform: scale(1.05);
  box-shadow: 0 0 80px rgba(255, 0, 85, 0.8);
}

.active-visualizer .loader::before {
  background: radial-gradient(circle at 30% 30%, rgba(255, 0, 85, 0.8), rgba(255, 0, 85, 0.4) 60%, rgba(150, 0, 55, 0.1));
}

/* AI Speaking visualizer hover effect */
.ai-speaking .loader::before {
  background: radial-gradient(circle at 30% 30%, rgba(255, 0, 85, 0.8), rgba(255, 0, 85, 0.4) 60%, rgba(85, 0, 51, 0.1));
}

.loader::before {
  content: "";
  position: absolute;
  inset: -5%;
  border-radius: 50%;
  background: transparent;
  filter: blur(25px);
  opacity: 0.8;
  background: radial-gradient(circle at 30% 30%, rgba(0, 221, 255, 0.8), rgba(0, 80, 255, 0.4) 60%, rgba(0, 0, 150, 0.1));
  z-index: -1;
  transform: scale(1.05);
  pointer-events: none;
}

.loader::after {
  content: "";
  position: absolute;
  inset: 10%;
  border-radius: 40% 60% 60% 40% / 50% 50% 50% 50%;
  background: rgba(255, 255, 255, 0.1);
  filter: blur(5px);
  transform: rotate(-15deg);
  animation: highlight 6s ease-in-out infinite alternate;
}

/* Enhanced animations for the visualizer */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes wiggle {
  0% {
    border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
  }
  25% {
    border-radius: 65% 35% 50% 50% / 40% 60% 40% 60%;
  }
  50% {
    border-radius: 40% 60% 70% 30% / 60% 40% 60% 40%;
  }
  75% {
    border-radius: 55% 45% 40% 60% / 45% 55% 50% 50%;
  }
  100% {
    border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
  }
}

@keyframes highlight {
  0% {
    opacity: 0.2;
    transform: scale(0.9) rotate(-15deg);
  }
  50% {
    opacity: 0.25;
    transform: scale(1) rotate(10deg);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.95) rotate(-5deg);
  }
}

/* Split Layout Transition Effects */
.split-layout-animation {
  transition: all 0.8s cubic-bezier(0.65, 0, 0.35, 1);
}

.visualizer-container {
  position: relative;
  overflow: hidden;
}

.visualizer-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 1px;
  background: linear-gradient(to bottom, transparent, rgba(var(--foreground), 0.1), transparent);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.split-active .visualizer-container::after {
  opacity: 1;
}

/* Enhance the glow effect for the orb */
.enhanced-glow {
  filter: drop-shadow(0 0 15px rgba(0, 221, 255, 0.4)) 
          drop-shadow(0 0 50px rgba(0, 150, 255, 0.2));
}

.active-visualizer .enhanced-glow {
  filter: drop-shadow(0 0 15px rgba(255, 0, 85, 0.4)) 
          drop-shadow(0 0 50px rgba(255, 0, 85, 0.2));
}

/* Add some particle effects */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(0, 200, 255, 0.5);
  border-radius: 50%;
  opacity: 0;
}

.active-visualizer .particle {
  background: rgba(255, 50, 100, 0.5);
}

.particle:nth-child(1) {
  top: 20%;
  left: 60%;
  animation: particle-animation-1 8s infinite;
}

.particle:nth-child(2) {
  top: 40%;
  left: 30%;
  animation: particle-animation-2 9s infinite;
}

.particle:nth-child(3) {
  top: 70%;
  left: 50%;
  animation: particle-animation-3 10s infinite;
}

@keyframes particle-animation-1 {
  0% { transform: translate3d(0, 0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate3d(50px, -50px, 0); opacity: 0; }
}

@keyframes particle-animation-2 {
  0% { transform: translate3d(0, 0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate3d(-70px, -30px, 0); opacity: 0; }
}

@keyframes particle-animation-3 {
  0% { transform: translate3d(0, 0, 0); opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { transform: translate3d(40px, -60px, 0); opacity: 0; }
}

/* Chat interface animation */
.chat-slide-in {
  animation: slideInFromRight 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Water drop glowing pulse */
.pulse-glow {
  animation: pulseGlow 3s infinite;
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 40px rgba(0, 221, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 70px rgba(0, 221, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 40px rgba(0, 221, 255, 0.5);
  }
}

.active-visualizer .pulse-glow {
  animation: pulseGlowActive 1.5s infinite;
}

@keyframes pulseGlowActive {
  0% {
    box-shadow: 0 0 40px rgba(255, 0, 85, 0.5);
  }
  50% {
    box-shadow: 0 0 70px rgba(255, 0, 85, 0.8);
  }
  100% {
    box-shadow: 0 0 40px rgba(255, 0, 85, 0.5);
  }
}

/* Sign In Button */
.button {
  cursor: pointer;
  font-size: 1rem;
  border-radius: 12px;
  border: none;
  padding: 2px;
  background: radial-gradient(circle 80px at 80% -10%, #ffffff, #181b1b);
  position: relative;
}
.button::after {
  content: "";
  position: absolute;
  width: 65%;
  height: 60%;
  border-radius: 120px;
  top: 0;
  right: 0;
  box-shadow: 0 0 20px #ffffff38;
  z-index: -1;
}

.blob1 {
  position: absolute;
  width: 50px;
  height: 100%;
  border-radius: 12px;
  bottom: 0;
  left: 0;
  background: radial-gradient(
    circle 60px at 0% 100%,
    #3fe9ff,
    #0000ff80,
    transparent
  );
  box-shadow: -10px 10px 30px #0051ff2d;
}

.blob2 {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  bottom: 0;
  right: 0;
  z-index: 1;
}

.inner {
  padding: 8px 16px;
  border-radius: 10px;
  color: #fff;
  z-index: 3;
  position: relative;
  background: radial-gradient(circle 80px at 80% -50%, #777777, #0f1111);
  font-size: 0.875rem;
}
.inner::before {
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  border-radius: 14px;
  background: radial-gradient(
    circle 60px at 0% 100%,
    #00e1ff1a,
    #0000ff11,
    transparent
  );
  position: absolute;
}

/* Add a fade-in-out animation for notifications */
@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(10px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

.animate-fade-in-out {
  animation: fadeInOut 8s ease-in-out;
  animation-fill-mode: forwards;
}

/* Fallback mode visualization styles */
.fallback-mode .loader {
  background: radial-gradient(circle at 30% 30%, #22c55e, #10b981 40%, #059669 60%, #047857);
  box-shadow: 0 0 60px rgba(20, 184, 166, 0.6);
}

.fallback-mode.active-visualizer .loader {
  background: radial-gradient(circle at 30% 30%, #10b981, #059669 40%, #047857 60%, #064e3b);
  box-shadow: 0 0 60px rgba(5, 150, 105, 0.6);
}

.fallback-mode .loader::before {
  background: radial-gradient(circle at 30% 30%, rgba(20, 184, 166, 0.8), rgba(5, 150, 105, 0.4) 60%, rgba(4, 120, 87, 0.1));
}

.fallback-mode.active-visualizer .loader::before {
  background: radial-gradient(circle at 30% 30%, rgba(16, 185, 129, 0.8), rgba(5, 150, 105, 0.4) 60%, rgba(6, 95, 70, 0.1));
}
